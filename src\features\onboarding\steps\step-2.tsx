import React, { useRef, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, Upload, AlertCircle } from 'lucide-react';
import { useOnboarding } from '@/features/onboarding/onboarding-context';
import { useS3ImageUpload } from '@/hooks/use-s3-image-upload';
import Image from 'next/image';

export default function Step2() {
  const { step, setStep, data, setData } = useOnboarding();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);

  // S3 upload hook - simplified!
  const { imageUrl, isUploading, progress, error, uploadImage } = useS3ImageUpload();

  const handleFileSelect = async (file: File) => {
    if (!file) {
      return;
    }

    // Check if it's a supported image type
    const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!supportedTypes.includes(file.type)) {
      // Let the hook handle the validation and show the proper error message
      await uploadImage(file);
      return;
    }

    // Store file reference for form data
    setData({
      profilePhoto: file,
    });

    // Upload to S3 with success callback to update context
    const success = await uploadImage(file, (s3Url) => {
      setData({
        profilePhotoS3Url: s3Url,
      });
    });

    if (success) {
      console.log('Upload successful!');
    } else {
      console.log('Upload failed');
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);

    const file = e.dataTransfer.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  // Computed values - much simpler now!
  const isValid = imageUrl || data.profilePhoto; // Valid if we have image URL or file
  const showUploadProgress = isUploading && progress > 0;

  return (
    <div className="flex flex-col w-full max-w-md mx-auto">
      <h1 className="text-2xl font-bold mb-4 mt-2">Choose a profile photo</h1>

      {imageUrl ? (
        /* Photo Uploaded State */
        <div className="border-2 border-solid rounded-xl flex flex-col items-center justify-center py-10 mb-10 bg-card border-input">
          {/* Success message with upload status */}
          <div className="flex items-center gap-2 mb-4">
            {!isUploading ? (
              <>
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span className="text-sm font-medium text-green-600">Uploaded successfully!</span>
              </>
            ) : (
              <>
                <Upload className="w-5 h-5 text-blue-600" />
                <span className="text-sm font-medium text-blue-600">Uploading...</span>
              </>
            )}
          </div>

          {/* Upload Progress */}
          {showUploadProgress && (
            <div className="w-full max-w-xs mb-4">
              <Progress value={progress} className="h-2" />
              <p className="text-sm text-muted-foreground text-center mt-1">
                {progress}% uploaded
              </p>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <Alert className="max-w-xs mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-sm">
                {error}
              </AlertDescription>
            </Alert>
          )}

          {/* Profile Image Preview */}
          <div className="w-24 h-24 rounded-full overflow-hidden mb-4">
            <Image
              src={imageUrl}
              alt="Profile preview"
              width={96}
              height={96}
              className="w-full h-full object-cover"
            />
          </div>

          <Button
            type="button"
            variant="secondary"
            className="rounded-full px-6 py-2 text-base font-medium bg-muted text-foreground"
            onClick={handleUploadClick}
            disabled={isUploading}
          >
            {isUploading ? 'Uploading...' : 'Change Photo'}
          </Button>
        </div>
      ) : (
        /* Initial Upload State */
        <div
          className={`border-2 border-dashed rounded-xl flex flex-col items-center justify-center py-10 mb-10 bg-card transition cursor-pointer ${
            isDragOver
              ? 'border-primary bg-primary/5'
              : 'border-input hover:border-primary'
          }`}
          onClick={handleUploadClick}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
        >
          <div className="text-base font-semibold mb-1 text-foreground">Add a profile photo</div>
          <div className="text-xs text-muted-foreground mb-4">Drag and drop or click to upload</div>

          {/* Upload Progress for initial state */}
          {showUploadProgress && (
            <div className="w-full max-w-xs mb-4">
              <Progress value={progress} className="h-2" />
              <p className="text-sm text-muted-foreground text-center mt-1">
                {progress}% uploaded
              </p>
            </div>
          )}

          {/* Error Message for initial state */}
          {error && (
            <Alert className="max-w-xs mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-sm">
                {error}
              </AlertDescription>
            </Alert>
          )}

          <Button
            type="button"
            variant="secondary"
            className="rounded-full px-6 py-2 text-base font-medium bg-muted text-foreground"
            onClick={e => { e.stopPropagation(); handleUploadClick(); }}
            disabled={isUploading}
          >
            {isUploading ? 'Uploading...' : 'Upload a photo'}
          </Button>
        </div>
      )}

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".jpg,.jpeg,.png,.webp,image/jpeg,image/png,image/webp"
        className="hidden"
        onChange={handleFileChange}
      />

      <div className="flex w-full justify-between gap-2">
        <Button variant="outline" className="w-32" onClick={() => setStep(step - 1)}>Back</Button>
        <Button
          className="w-32"
          onClick={() => setStep(step + 1)}
          disabled={!isValid || isUploading}
        >
          {isUploading ? 'Uploading...' : 'Continue'}
        </Button>
      </div>
    </div>
  );
}
