"use client"

import { useMusicPlayer } from "@/contexts/music-player-context/music-player-context"
import { ReactNode } from "react"

interface MusicPlayerAwareLayoutProps {
  children: ReactNode
}

export function MusicPlayerAwareLayout({ children }: MusicPlayerAwareLayoutProps) {
  const { currentSong, isExpanded } = useMusicPlayer()

  // Calculate bottom offset based on music player state
  const musicPlayerHeight = currentSong ? (isExpanded ? 300 : 64) : 0

  return (
    <div
      className="flex safari-safe-height w-full overflow-hidden"
      style={{
        '--music-player-height': `${musicPlayerHeight}px`
      } as React.CSSProperties}
    >
      {children}
    </div>
  )
}
