import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/contexts/theme/theme-context";
import { I18nProvider } from "@/contexts/i18n/i18n-context";
import { MusicPlayerProvider } from "@/contexts/music-player-context/music-player-context"
import { defaultLocale } from "@/i18n/config";
import AppGate from "@/components/auth/app-gate";
import { AuthProvider } from "@/contexts/auth/auth-context";
import Providers from "./providers";
import { ConditionalLayout } from "@/components/layout/conditional-layout";
import { ViewportHeightInitializer } from "@/components/shared/viewport-height-initializer";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const arvo = Arvo({
  variable: "--font-arvo",
  subsets: ["latin"],
  weight: ["400", "700"],
});

const lato = Lato({
  variable: "--font-lato",
  subsets: ["latin"],
  weight: ["400", "700"],
});

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  weight: ["400", "500"],
});

export const metadata: Metadata = {
  title: "Smash",
  description: "A modern music application with dark/light theme support",
  viewport: {
    width: "device-width",
    initialScale: 1,
    maximumScale: 1,
    userScalable: false,
    // iOS Safari specific viewport handling
    viewportFit: "cover",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // For static export, we use the default locale
  // Client-side locale switching will be handled by the LanguageToggle component
  const locale = defaultLocale;

  return (
    <html lang={locale}>
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${arvo.variable} ${lato.variable} ${inter.variable} antialiased`}
      >
        <Providers>
          <ViewportHeightInitializer />
          <ThemeProvider>
            <AuthProvider>
              <AppGate>
                <I18nProvider>
                  <MusicPlayerProvider>
                    <ConditionalLayout>
                      {children}
                    </ConditionalLayout>
                  </MusicPlayerProvider>
                </I18nProvider>
              </AppGate>
            </AuthProvider>
          </ThemeProvider>
        </Providers>
      </body>
    </html>
  );
}
