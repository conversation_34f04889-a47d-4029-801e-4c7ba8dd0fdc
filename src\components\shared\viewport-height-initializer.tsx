"use client"

import { useEffect } from 'react';
import { initializeViewportHeight } from '@/lib/viewport-height';

/**
 * Viewport Height Initializer Component
 * 
 * This component initializes the viewport height handling for Safari iOS.
 * It should be included once in the app layout to ensure proper viewport
 * height calculations across the entire application.
 */
export function ViewportHeightInitializer() {
  useEffect(() => {
    initializeViewportHeight();
  }, []);

  // This component doesn't render anything
  return null;
}
