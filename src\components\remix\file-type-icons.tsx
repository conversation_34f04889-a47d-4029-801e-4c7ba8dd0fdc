import React from 'react';
import { Music, FileAudio } from 'lucide-react';

interface FileTypeIconProps {
  extension: string;
  className?: string;
}

export function FileTypeIcon({ extension, className = "h-6 w-6" }: FileTypeIconProps) {
  const ext = extension.toLowerCase();
  
  switch (ext) {
    case 'wav':
      return (
        <div className={`${className} flex items-center justify-center bg-blue-100 text-blue-600 rounded`}>
          <FileAudio className="h-4 w-4" />
        </div>
      );
    case 'mp3':
      return (
        <div className={`${className} flex items-center justify-center bg-green-100 text-green-600 rounded`}>
          <Music className="h-4 w-4" />
        </div>
      );
    default:
      return (
        <div className={`${className} flex items-center justify-center bg-gray-100 text-gray-600 rounded`}>
          <FileAudio className="h-4 w-4" />
        </div>
      );
  }
}
