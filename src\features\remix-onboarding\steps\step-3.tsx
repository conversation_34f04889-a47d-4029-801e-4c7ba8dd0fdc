'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useRemixOnboarding, validateStep3 } from '../remix-onboarding-context';

export default function Step3() {
  const { data, updateData, nextStep, prevStep } = useRemixOnboarding();
  const [bio, setBio] = useState(data.bio);
  const [location, setLocation] = useState(data.location);
  const [phoneNumber, setPhoneNumber] = useState(data.phoneNumber || '');
  const [phoneError, setPhoneError] = useState('');

  const validatePhoneNumber = (phone: string): string => {
    if (!phone.trim()) return 'Phone number is required';

    // Simple regex for country code (1-3 digits) followed by number (7-12 digits)
    const phoneRegex = /^\d{1,3}\s?\d{7,12}$/;

    if (!phoneRegex.test(phone.replace(/[\s()-]/g, ''))) {
      return 'Please enter a valid phone number with country code (e.g., 1 1234567890)';
    }

    return '';
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPhoneNumber(e.target.value);

    // Clear error when user starts typing
    if (phoneError) {
      setPhoneError('');
    }
  };

  const handlePhoneBlur = () => {
    const error = validatePhoneNumber(phoneNumber);
    setPhoneError(error);
  };

  const handleContinue = () => {
    const updatedData = { bio, location, phoneNumber };
    updateData(updatedData);

    if (validateStep3({ ...data, ...updatedData })) {
      nextStep();
    }
  };

  const handleBack = () => {
    prevStep();
  };

  const isValid = bio.trim().length > 0 && location.trim().length > 0 && phoneNumber.trim().length > 0 && !phoneError;

  return (
    <div className="space-y-6">
      {/* Title */}
      <div className="flex flex-col justify-start items-start">
        <h1 className="max-w-[328.81px] flex justify-center text-foreground text-3xl font-bold font-arvo leading-10">
          Describe yourself
        </h1>
      </div>

      {/* Form Fields */}
      <div className="space-y-6">
        {/* Bio Field */}
        <div className="space-y-2">
          <Label
            htmlFor="bio"
            className="text-muted-foreground text-sm font-normal font-arvo "
          >
            Write a short bio best describing you *
          </Label>
          <Textarea
            id="bio"
            value={bio}
            onChange={(e) => {
              if (e.target.value.length <= 1000) {
                setBio(e.target.value);
              }
            }}
            placeholder="e.g. I am an enthusiastic song writer"
            rows={4}
            maxLength={1000}
            className="w-full px-4 py-3.5 bg-card border border-border rounded text-sm font-lato text-card-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-1 focus:ring-primary resize-none"
          />
          <p className="text-xs text-muted-foreground font-lato">
            {bio.length}/1000 characters
          </p>
        </div>

        {/* Location Field */}
        <div className="space-y-2">
          <Label
            htmlFor="location"
            className="text-muted-foreground text-sm font-normal font-arvo"
          >
            What&apos;s your location? *
          </Label>
          <Input
            id="location"
            type="text"
            value={location}
            onChange={(e) => setLocation(e.target.value)}
            placeholder="e.g. Los Angeles"
            className="w-full px-4 py-3 bg-card border border-border rounded text-sm font-lato text-card-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-1 focus:ring-primary"
          />
        </div>

        {/* Phone Number Field */}
        <div className="flex flex-col justify-start items-start gap-1.5">
          <Label
            htmlFor="phone-number"
            className="text-muted-foreground text-sm font-normal font-arvo"
          >
            What&apos;s your phone number? *
          </Label>
          <Input
            id="phone-number"
            type="tel"
            value={phoneNumber}
            onChange={handlePhoneChange}
            onBlur={handlePhoneBlur}
            placeholder="e.g. *************"
            className={`w-full px-4 py-2.5 bg-card border rounded text-sm font-lato text-card-foreground placeholder:text-muted-foreground focus:ring-1 ${
              phoneError
                ? 'border-destructive focus:border-destructive focus:ring-destructive'
                : 'border-border focus:border-primary focus:ring-primary'
            }`}
          />
          {phoneError && (
            <p className="text-xs text-destructive font-lato">
              {phoneError}
            </p>
          )}
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between items-center pt-6">
        <Button
          onClick={handleBack}
          variant="outline"
          className="px-6 py-3 font-arvo font-bold text-base border-muted-foreground text-muted-foreground hover:bg-muted"
        >
          Back
        </Button>
        <Button
          onClick={handleContinue}
          disabled={!isValid}
          className="px-6 py-3 bg-primary text-primary-foreground font-arvo font-bold text-base hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Continue
        </Button>
      </div>
    </div>
  );
}
