"use client"

import React from "react"
import Image from "next/image"
import { useMusicPlayer } from "@/contexts/music-player-context/music-player-context"
import { Button } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import {
  Play,
  Pause,
  SkipBack,
  SkipForward,
  Volume2,
  Volume1,
  VolumeX,
  Maximize2,
  Minimize2,
  // ExternalLink, // Commented out as we removed the "View Full Song Page" button
} from "lucide-react"
import { cn } from "@/lib/utils"
import { MusicPlayerDarkThemeWrapper } from "./music-player-dark-theme-wrapper"

export function MusicPlayer() {
  const {
    currentSong,
    isPlaying,
    volume,
    progress,
    duration,
    isExpanded,
    navigationDisabled,
    expandDisabled,
    togglePlay,
    setVolume,
    seekTo,
    nextSong,
    prevSong,
    toggleExpanded,
  } = useMusicPlayer()



  // Format time in MM:SS
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`
  }



  // Volume icon based on level
  const VolumeIcon = volume === 0 ? VolumeX : volume < 0.5 ? Volume1 : Volume2

  if (!currentSong) return null

  return (
    <MusicPlayerDarkThemeWrapper>
      <div
        className={cn(
          "w-full bg-background border-t transition-all duration-300",
          isExpanded ? "h-[300px]" : "h-20",
        )}
      >
      {/* Mini Player (Always visible) */}
      <div className="h-16 px-4 flex items-center gap-4 bg-background border-t border-border">
        {/* Left Section: Album Art + Song Info */}
        <div className="flex items-center gap-3 min-w-0 flex-1">
          <div className={cn("cursor-pointer flex-shrink-0", expandDisabled && "cursor-default")} onClick={expandDisabled ? undefined : toggleExpanded}>
            <div className="w-12 h-12 relative rounded-md overflow-hidden bg-muted">
              <Image
                src={currentSong.albumArt || "/placeholder.svg"}
                alt={currentSong.album}
                fill
                className="object-cover"
              />
            </div>
          </div>

          <div className={cn("min-w-0 flex-1", expandDisabled ? "cursor-default" : "cursor-pointer")} onClick={expandDisabled ? undefined : toggleExpanded}>
            <p className="font-medium text-sm line-clamp-1 truncate text-foreground">{currentSong.title}</p>
            <p className="text-xs text-muted-foreground line-clamp-1 truncate">{currentSong.artist}</p>
          </div>
        </div>

        {/* Center Section: Controls + Progress */}
        <div className="hidden sm:flex flex-col items-center gap-2 flex-1 max-w-md mt-4">
          {/* Controls Row */}
          <div className="flex items-center gap-4">
            {!navigationDisabled && (
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-muted-foreground hover:text-foreground"
                onClick={prevSong}
                aria-label="Previous song"
              >
                <SkipBack className="h-4 w-4" />
              </Button>
            )}

            <Button
              size="icon"
              className="h-10 w-10 rounded-full bg-foreground text-background hover:bg-foreground/90"
              onClick={togglePlay}
              aria-label={isPlaying ? "Pause" : "Play"}
            >
              {isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5 ml-0.5" />}
            </Button>

            {!navigationDisabled && (
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-muted-foreground hover:text-foreground"
                onClick={nextSong}
                aria-label="Next song"
              >
                <SkipForward className="h-4 w-4" />
              </Button>
            )}
          </div>

          {/* Progress Bar Row */}
          <div className="w-full flex items-center gap-2">
            <span className="text-xs text-muted-foreground w-10 text-right">
              {formatTime(progress)}
            </span>
            <Slider
              value={[progress]}
              max={duration || 100}
              step={1}
              className="flex-1"
              onValueChange={(value) => seekTo(value[0])}
            />
            <span className="text-xs text-muted-foreground w-10">
              {formatTime(duration)}
            </span>
          </div>
        </div>

        {/* Right Section: Volume + Additional Controls */}
        <div className="hidden sm:flex items-center gap-2 min-w-0 flex-1 justify-end">
          <div className="flex items-center gap-2">
            <VolumeIcon className="h-4 w-4 text-muted-foreground" />
            <div className="w-20">
              <Slider
                value={[volume]}
                max={1}
                step={0.01}
                onValueChange={(value) => setVolume(value[0])}
                className="w-full"
              />
            </div>
          </div>

          {!expandDisabled && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-muted-foreground hover:text-foreground"
              onClick={toggleExpanded}
              aria-label={isExpanded ? "Minimize player" : "Expand player"}
            >
              {isExpanded ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
            </Button>
          )}
        </div>

        {/* Mobile: Simple layout */}
        <div className="flex sm:hidden items-center gap-3">
          <Button
            variant="ghost"
            size="icon"
            className="h-10 w-10 rounded-full bg-foreground text-background"
            onClick={togglePlay}
            aria-label={isPlaying ? "Pause" : "Play"}
          >
            {isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5 ml-0.5" />}
          </Button>
        </div>
      </div>

      {/* Mobile Progress Bar - Bottom */}
      <div className="sm:hidden px-4 pb-2">
        <Slider
          value={[progress]}
          max={duration || 100}
          step={1}
          onValueChange={(value) => seekTo(value[0])}
          className="w-full"
        />
      </div>

      {/* Expanded Player */}
      {isExpanded && (
        <div className="p-3 sm:p-4 md:p-6 h-[calc(300px-4rem)] flex flex-col gap-2 sm:gap-3">
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 md:gap-6">
            <div className="flex-shrink-0 self-center sm:self-start">
              <div className="relative w-20 h-20 sm:w-24 sm:h-24 md:w-32 md:h-32 rounded-lg overflow-hidden shadow-lg">
                <Image
                  src={currentSong.albumArt || "/placeholder.svg"}
                  alt={currentSong.album}
                  fill
                  className="object-cover"
                />
              </div>
            </div>

            <div className="flex-1 flex flex-col min-w-0 space-y-2 sm:space-y-3">
              <div className="text-center sm:text-left">
                <h3 className="text-sm sm:text-base md:text-lg font-bold line-clamp-1">{currentSong.title}</h3>
                <p className="text-xs sm:text-sm text-muted-foreground line-clamp-1">{currentSong.artist}</p>
                <p className="text-xs text-muted-foreground line-clamp-1">{currentSong.album}</p>
              </div>

              <div className="flex items-center justify-center gap-2 sm:gap-3">
                {!navigationDisabled && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 sm:h-8 sm:w-8"
                    onClick={prevSong}
                    aria-label="Previous song"
                  >
                    <SkipBack className="h-4 w-4" />
                  </Button>
                )}
                <Button
                  size="icon"
                  className="h-8 w-8 sm:h-10 sm:w-10 rounded-full"
                  onClick={togglePlay}
                  aria-label={isPlaying ? "Pause" : "Play"}
                >
                  {isPlaying ? <Pause className="h-4 w-4 sm:h-5 sm:w-5" /> : <Play className="h-4 w-4 sm:h-5 sm:w-5" />}
                </Button>
                {!navigationDisabled && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 sm:h-8 sm:w-8"
                    onClick={nextSong}
                    aria-label="Next song"
                  >
                    <SkipForward className="h-4 w-4" />
                  </Button>
                )}
              </div>

              {/* <div className="space-y-1">
                <h4 className="text-xs font-medium">Credits:</h4>
                <div className="text-[10px] sm:text-xs text-muted-foreground space-y-0.5">
                  {currentSong.credits?.producer && <p>Producer: {currentSong.credits.producer}</p>}
                  {currentSong.credits?.writer && <p>Writer: {currentSong.credits.writer}</p>}
                  {currentSong.credits?.engineer && <p>Engineer: {currentSong.credits.engineer}</p>}
                </div>
                <p className="text-[10px] sm:text-xs text-muted-foreground">Audio Source: MusicBrainz</p>
              </div> */}
            </div>
          </div>

          {/* Bottom scrubber for expanded player */}
          <div className="flex items-center gap-2 mt-auto pt-2 border-t">
            <span className="text-xs text-muted-foreground w-8 text-right text-[10px] sm:text-xs">
              {formatTime(progress)}
            </span>
            <Slider
              value={[progress]}
              max={duration}
              step={1}
              className="flex-1"
              onValueChange={(value) => seekTo(value[0])}
            />
            <span className="text-xs text-muted-foreground w-8 text-[10px] sm:text-xs">
              {formatTime(duration)}
            </span>
          </div>
        </div>
      )}
      </div>
    </MusicPlayerDarkThemeWrapper>
  )
}
