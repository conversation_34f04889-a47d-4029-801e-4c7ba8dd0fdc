/**
 * S3 Image Upload Utilities
 * Handles image validation, pre-signed URL fetching, and S3 upload functionality
 */

import type {
  ImageUploadConfig,
  ImageValidationResult,
  ImageUploadResult,
  ImageUploadOptions,
  S3PreSignedUrlRequest,
  S3PreSignedUrlResponse,
  S3PreSignedUrlBody,
  UploadProgressCallback,
} from '@/types/s3-upload';

import {
  DEFAULT_IMAGE_UPLOAD_CONFIG,
  S3_UPLOAD_API_CONFIG,
} from '@/types/s3-upload';

/**
 * Validate an image file against upload requirements
 */
export function validateImageFile(
  file: File,
  config: ImageUploadConfig = DEFAULT_IMAGE_UPLOAD_CONFIG
): Promise<ImageValidationResult> {
  return new Promise((resolve) => {
    const warnings: string[] = [];

    // Check file type
    const isValidType = config.allowedTypes.includes(file.type) ||
      config.allowedExtensions.some(ext => 
        file.name.toLowerCase().endsWith(ext.toLowerCase())
      );

    if (!isValidType) {
      return resolve({
        isValid: false,
        error: `Please select a supported image file. We support: JPG, JPEG, PNG, and WebP formats only.`,
      });
    }

    // Check file size
    if (file.size > config.maxSizeBytes) {
      const maxSizeMB = Math.round(config.maxSizeBytes / (1024 * 1024));
      return resolve({
        isValid: false,
        error: `Image size must be less than ${maxSizeMB}MB. Current size: ${formatFileSize(file.size)}`,
      });
    }

    // Check file name
    if (!file.name || file.name.trim().length === 0) {
      return resolve({
        isValid: false,
        error: 'Image file must have a valid name',
      });
    }

    // Check image dimensions
    const img = new Image();
    const objectUrl = URL.createObjectURL(file);

    img.onload = () => {
      URL.revokeObjectURL(objectUrl);

      if (img.width < config.minWidth || img.height < config.minHeight) {
        return resolve({
          isValid: false,
          error: `Image dimensions must be at least ${config.minWidth}x${config.minHeight}px. Current: ${img.width}x${img.height}px`,
        });
      }

      if (img.width > config.maxWidth || img.height > config.maxHeight) {
        warnings.push(`Large image detected (${img.width}x${img.height}px). Consider resizing for faster upload.`);
      }

      resolve({
        isValid: true,
        warnings: warnings.length > 0 ? warnings : undefined,
      });
    };

    img.onerror = () => {
      URL.revokeObjectURL(objectUrl);
      resolve({
        isValid: false,
        error: 'Invalid image file. Please select a valid image.',
      });
    };

    img.src = objectUrl;
  });
}

/**
 * Generate a unique filename for S3 upload
 */
export function generateS3Filename(originalFile: File): string {
  const timestamp = Date.now();
  const randomSuffix = Math.random().toString(36).substring(2, 8);
  const extension = getFileExtension(originalFile.name);
  
  return `${S3_UPLOAD_API_CONFIG.filePathPrefix}profile_picture_${timestamp}_${randomSuffix}.${extension}`;
}

/**
 * Fetch pre-signed URL from API
 */
export async function fetchPreSignedUrl(
  filename: string
): Promise<S3PreSignedUrlBody> {
  const request: S3PreSignedUrlRequest = { filename };

  const response = await fetch(S3_UPLOAD_API_CONFIG.endpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(request),
  });

  if (!response.ok) {
    throw new Error(`API request failed: ${response.status} ${response.statusText}`);
  }

  const data: S3PreSignedUrlResponse = await response.json();

  if (data.statusCode !== 200 || !data.body) {
    throw new Error('Invalid API response: missing response body');
  }

  // Parse the JSON string in the body
  let parsedBody: S3PreSignedUrlBody;
  try {
    parsedBody = JSON.parse(data.body);
  } catch {
    throw new Error('Invalid API response: body is not valid JSON');
  }

  if (!parsedBody.uploadUrl || !parsedBody.viewUrl) {
    throw new Error('Invalid API response: missing upload URL or view URL');
  }

  return parsedBody;
}

/**
 * Upload file to S3 using pre-signed URL
 */
export async function uploadToS3(
  file: File,
  uploadUrl: string,
  onProgress?: UploadProgressCallback
): Promise<void> {
  const xhr = new XMLHttpRequest();

  return new Promise<void>((resolve, reject) => {
    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable && onProgress) {
        const progress = Math.round((event.loaded / event.total) * 100);
        onProgress(progress);
      }
    });

    xhr.addEventListener('load', () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        resolve();
      } else {
        reject(new Error(`S3 upload failed: ${xhr.status} ${xhr.statusText}`));
      }
    });

    xhr.addEventListener('error', () => {
      reject(new Error('S3 upload failed: Network error'));
    });

    xhr.addEventListener('timeout', () => {
      reject(new Error('S3 upload failed: Request timeout'));
    });

    xhr.open('PUT', uploadUrl);
    xhr.setRequestHeader('Content-Type', file.type);
    xhr.timeout = 60000; // 60 second timeout
    xhr.send(file);
  });
}

/**
 * Complete image upload process (validation + pre-signed URL + S3 upload)
 */
export async function uploadImageToS3(
  file: File,
  options: ImageUploadOptions = {}
): Promise<ImageUploadResult> {
  const { onProgress } = options;

  try {
    // Step 1: Validate image
    onProgress?.(5);
    const validation = await validateImageFile(file);
    if (!validation.isValid) {
      return {
        success: false,
        error: validation.error,
      };
    }

    // Step 2: Generate filename and get pre-signed URL
    onProgress?.(15);
    const filename = generateS3Filename(file);
    const preSignedResponse = await fetchPreSignedUrl(filename);

    // Step 3: Upload to S3
    onProgress?.(25);
    await uploadToS3(
      file,
      preSignedResponse.uploadUrl,
      (progress) => {
        // Map S3 upload progress (25-100%)
        const mappedProgress = 25 + (progress * 0.75);
        onProgress?.(Math.round(mappedProgress));
      }
    );

    // Step 4: Use the viewUrl for displaying the image
    return {
      success: true,
      url: preSignedResponse.viewUrl, // Use viewUrl for displaying the image
      objectKey: preSignedResponse.objectKey,
    };
  } catch (error) {
    console.error('Image upload failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Upload failed. Please try again.',
    };
  }
}

/**
 * Utility function to format file size
 */
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Get file extension from filename
 */
function getFileExtension(filename: string): string {
  return filename.split('.').pop()?.toLowerCase() || '';
}
