"use client";

import { usePathname } from "next/navigation";
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/shared/side-bar/app-sidebar";
import Header from "@/components/shared/header";
import { MusicPlayer } from "@/components/shared/music-player/music-player";
import { MusicPlayerAwareLayout } from "@/components/shared/music-player-aware-layout";
import { getBuildConfig } from "@/lib/build-config";
import { isStandaloneRoute } from "@/lib/auth-routes";

interface ConditionalLayoutProps {
  children: React.ReactNode;
}

export function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const pathname = usePathname();
  const config = getBuildConfig();
  const isStandalone = isStandaloneRoute(pathname);
  
  // For remix build mode, always use standalone layout
  // For main build mode, use standalone layout only for specific routes
  const shouldUseStandaloneLayout = config.mode === 'remix' || isStandalone;

  if (shouldUseStandaloneLayout) {
    // Standalone layout - no sidebar or header, but keep music player
    return (
      <MusicPlayerAwareLayout>
        <div className="flex flex-col h-full w-full">
          <div className="flex-1 overflow-auto">
            {children}
          </div>
          <MusicPlayer />
        </div>
      </MusicPlayerAwareLayout>
    );
  }

  // Full layout with sidebar, header, and music player
  return (
    <SidebarProvider>
      <MusicPlayerAwareLayout>
        <AppSidebar />
        <div className="flex-1 flex flex-col h-full overflow-hidden">
          <SidebarInset className="flex flex-col h-full overflow-hidden">
            <Header />
            <div className="flex-1 overflow-auto">
              {children}
            </div>
            <MusicPlayer />
          </SidebarInset>
        </div>
      </MusicPlayerAwareLayout>
    </SidebarProvider>
  );
}
