/**
 * Route Configuration for Authentication with Build Mode Support
 * Supports dual deployment architecture with conditional routing
 */

import { getBuildConfig } from './build-config';

// Base routes for main application
const MAIN_APP_PROTECTED_ROUTES = [
  '/applications',
  '/opportunities',
  '/opportunities/*',
  '/album',
  '/album/*',
  '/artist',
  '/artist/*',
  '/playlist',
  '/song',
  '/song/*',
  '/onboarding',
  '/discover',
  '/discover/*',
  '/recording'
];

// Base routes for remix functionality
const REMIX_PROTECTED_ROUTES = [
  '/remix/onboarding',
];

// Routes only for unauthenticated users (redirect to dashboard if authenticated)
const BASE_AUTH_ONLY_ROUTES = [
  '/login',
  '/signup',
  '/forgot-password',
];

// Remix-specific public routes
const REMIX_PUBLIC_ROUTES = [
  '/remix',
];

/**
 * Get protected routes based on current build mode
 */
export function getProtectedRoutes(): string[] {
  const config = getBuildConfig();

  if (config.mode === 'remix') {
    // Remix mode: only remix routes are protected
    return REMIX_PROTECTED_ROUTES;
  } else {
    // Main mode: all main app routes plus remix routes are protected
    return [...MAIN_APP_PROTECTED_ROUTES, ...REMIX_PROTECTED_ROUTES];
  }
}

/**
 * Get auth-only routes based on current build mode
 */
export function getAuthOnlyRoutes(): string[] {
  // Auth-only routes are the same for both modes
  return BASE_AUTH_ONLY_ROUTES;
}

/**
 * Get public routes based on current build mode
 */
export function getPublicRoutes(): string[] {
  const config = getBuildConfig();

  if (config.mode === 'remix') {
    // Remix mode: remix routes are public
    return REMIX_PUBLIC_ROUTES;
  } else {
    // Main mode: no public routes
    return [];
  }
}

/**
 * Get standalone routes (no header/sidebar) based on current build mode
 */
export function getStandaloneRoutes(): string[] {
  const config = getBuildConfig();

  if (config.mode === 'remix') {
    // Remix mode: all remix routes are standalone
    return ['/remix', '/remix/onboarding'];
  } else {
    // Main mode: no remix routes are standalone since they're blocked
    return [];
  }
}

// Legacy exports for backward compatibility (dynamically generated)
export const PROTECTED_ROUTES = getProtectedRoutes();
export const AUTH_ONLY_ROUTES = getAuthOnlyRoutes();
export const PUBLIC_ROUTES = getPublicRoutes();
export const STANDALONE_ROUTES = getStandaloneRoutes();

/**
 * Get all valid routes based on current build mode
 */
export function getAllValidRoutes(): string[] {
  const config = getBuildConfig();
  const protectedRoutes = getProtectedRoutes();
  const authOnlyRoutes = getAuthOnlyRoutes();
  const publicRoutes = getPublicRoutes();
  const standaloneRoutes = getStandaloneRoutes();

  const baseRoutes = [
    ...protectedRoutes,
    ...authOnlyRoutes,
    ...publicRoutes,
    ...standaloneRoutes,
    '/auth/callback', // Auth callback route
  ];

  // Add main app specific routes only if main app is enabled
  if (config.mode === 'main') {
    baseRoutes.push(
      '/opportunities/new',
      '/opportunities/posted'
    );
  }

  return baseRoutes;
}

// Legacy export for backward compatibility
export const ALL_VALID_ROUTES = getAllValidRoutes();

// Helper to normalize pathnames (remove trailing slash except for root)
function normalizePathname(pathname: string): string {
  if (pathname === '/') return '/';
  return pathname.replace(/\/$/, '');
}

/**
 * Check if a route requires authentication
 */
export function isProtectedRoute(pathname: string): boolean {
  const normalized = normalizePathname(pathname);
  const protectedRoutes = getProtectedRoutes();

  return protectedRoutes.some(route => {
    const normalizedRoute = normalizePathname(route);
    // Exact match for single routes
    if (normalizedRoute === normalized) return true;

    // For routes ending with wildcard, check if pathname starts with the base
    if (route.endsWith('/*')) {
      const basePath = route.slice(0, -2);
      return normalized.startsWith(basePath + '/');
    }

    // For other routes, check if pathname starts with route followed by '/' or is exact match
    return normalized === normalizedRoute || normalized.startsWith(normalizedRoute + '/');
  });
}

/**
 * Check if a route is auth-only (for unauthenticated users)
 */
export function isAuthOnlyRoute(pathname: string): boolean {
  const normalized = normalizePathname(pathname);
  const authOnlyRoutes = getAuthOnlyRoutes();
  return authOnlyRoutes.find(route => normalized === normalizePathname(route)) !== undefined;
}

/**
 * Check if a route is standalone (no header/sidebar)
 */
export function isStandaloneRoute(pathname: string): boolean {
  const normalized = normalizePathname(pathname);
  const standaloneRoutes = getStandaloneRoutes();
  return standaloneRoutes.find(route => normalized === normalizePathname(route)) !== undefined;
}

/**
 * Check if a route is public (accessible to all users regardless of auth state)
 */
export function isPublicRoute(pathname: string): boolean {
  const normalized = normalizePathname(pathname);
  const publicRoutes = getPublicRoutes();
  return publicRoutes.some(route => normalized === normalizePathname(route));
}

/**
 * Check if a route exists in the application
 */
export function isValidRoute(pathname: string): boolean {
  const normalized = normalizePathname(pathname);
  const allValidRoutes = getAllValidRoutes();

  // Check exact matches first
  const exactMatch = allValidRoutes.some(route => {
    const normalizedRoute = normalizePathname(route);
    return normalizedRoute === normalized;
  });

  if (exactMatch) return true;

  // Check wildcard routes
  return allValidRoutes.some(route => {
    if (route.endsWith('/*')) {
      const basePath = route.slice(0, -2);
      return normalized.startsWith(basePath + '/');
    }
    return false;
  });
}

/**
 * Check if a route is blocked in the current build mode
 */
export function isBlockedRoute(pathname: string): boolean {
  const normalized = normalizePathname(pathname);
  const config = getBuildConfig();

  if (config.mode === 'remix') {
    // In remix mode, block all main app routes
    return MAIN_APP_PROTECTED_ROUTES.some(route => {
      const normalizedRoute = normalizePathname(route);
      if (normalizedRoute === normalized) return true;

      if (route.endsWith('/*')) {
        const basePath = route.slice(0, -2);
        return normalized.startsWith(basePath + '/');
      }

      return normalized.startsWith(normalizedRoute + '/');
    });
  } else {
    // In main mode, block ALL remix routes
    const allRemixRoutes = ['/remix', '/remix/*'];
    return allRemixRoutes.some(route => {
      const normalizedRoute = normalizePathname(route);
      if (normalizedRoute === normalized) return true;

      if (route.endsWith('/*')) {
        const basePath = route.slice(0, -2);
        return normalized.startsWith(basePath + '/');
      }

      return normalized.startsWith(normalizedRoute + '/');
    });
  }
}

export function isOnboarding(pathname: string): boolean {
  return pathname.includes("/onboarding");
}

/**
 * Get redirect path based on auth state and current route
 */
export function getAuthRedirect(pathname: string, isAuthenticated: boolean): string | null {
  const normalized = normalizePathname(pathname);
  const config = getBuildConfig();

  // Check if route is blocked in current build mode - this is the key enforcement
  if (isBlockedRoute(normalized)) {
    // Redirect to the appropriate route for current build mode
    return config.routing.defaultUnauthenticatedRoute;
  }

  // First check if the route exists - if not, redirect based on auth state
  if (!isValidRoute(normalized)) {
    return isAuthenticated
      ? config.routing.defaultAuthenticatedRoute
      : config.routing.defaultUnauthenticatedRoute;
  }

  // Check if route is public (accessible to all)
  if (isPublicRoute(normalized)) {
    return null;
  }

  // Check if route requires authentication (both protected and standalone routes require auth)
  if ((isProtectedRoute(normalized) || isStandaloneRoute(normalized)) && !isAuthenticated) {
    return config.routing.defaultUnauthenticatedRoute;
  }

  // Check if authenticated user is trying to access auth-only routes
  if (isAuthenticated && isAuthOnlyRoute(normalized) && !isOnboarding(normalized)) {
    return config.routing.defaultAuthenticatedRoute;
  }

  return null;
}
