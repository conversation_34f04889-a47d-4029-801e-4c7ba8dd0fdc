import { Amplify } from 'aws-amplify';
import type { ResourcesConfig } from 'aws-amplify';
import { getCognitoConfig, validateBuildConfig } from './build-config';

// Get Cognito configuration based on current build mode
export const cognitoConfig = getCognitoConfig();

// Initialize AWS Amplify with Cognito configuration
export function initCognitoAuth() {
  // Validate configuration before initializing
  const validation = validateBuildConfig();
  if (!validation.isValid) {
    console.error('Build configuration validation failed:', validation.errors);
    throw new Error(`Invalid build configuration: ${validation.errors.join(', ')}`);
  }

  const amplifyConfig: ResourcesConfig = {
    Auth: {
      Cognito: {
        userPoolId: cognitoConfig.userPoolId,
        userPoolClientId: cognitoConfig.clientId,
        loginWith: {
          email: true,
          username: false
        }
        // Removed identityPoolId to avoid conflicts with API key auth
      }
    },
    API: {
      GraphQL: {
        endpoint: 'https://2xymbka4zzam3b347bgugpfp6a.appsync-api.us-east-2.amazonaws.com/graphql',
        region: 'us-east-2',
        defaultAuthMode: 'apiKey' as const,
        apiKey: 'da2-hu56cxlaxreutk6qxxkxhab7ly'
      }
    }
  };

  // Configure region separately or through environment
  process.env.AWS_REGION = cognitoConfig.region;

  Amplify.configure(amplifyConfig);
}