"use client";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Settings, LogIn, LogOut, User, Search, Loader2 } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";
import { useAuth as useAuthContext } from "@/contexts/auth/auth-context";
import Image from "next/image";
import ThemeToggle from "@/components/shared/theme/theme-toggle";
import LanguageToggle from "@/components/shared/language/language-toggle";
import { isAuthOnlyRoute, isOnboarding, isStandaloneRoute } from "@/lib/auth-routes";
import { useState, useRef, useEffect } from "react";
import { useQuery } from "@apollo/client";
import { SEARCH_QUERY } from "@/graphql/queries";

interface SearchResult {
  id: string;
  type: string;
  displayName: string;
  properties: string;
  searchScore: number;
}

export default function Header() {
  const router = useRouter();
  const { isAuthenticated, isLoading, user, signOut } = useAuthContext();

  // Search state
  const [searchTerm, setSearchTerm] = useState("");
  const [entityType, setEntityType] = useState("all");
  const [showResults, setShowResults] = useState(false);
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const searchRef = useRef<HTMLDivElement>(null);

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Search query
  const { data: searchData, loading: searchLoading } = useQuery(SEARCH_QUERY, {
    variables: {
      searchString: debouncedSearchTerm,
      entityType: entityType === "all" ? "" : entityType,
      limit: 5
    },
    skip: !debouncedSearchTerm.trim(),
  });

  const searchResults: SearchResult[] = searchData?.search || [];

  // Handle search input
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setShowResults(value.trim().length > 0);
  };

  // Handle result click
  const handleResultClick = (result: SearchResult) => {
    const { id, type } = result;
    setShowResults(false);
    setSearchTerm("");

    switch (type.toLowerCase()) {
      case 'artist':
        router.push(`/artist?id=${id}`);
        break;
      case 'album':
        router.push(`/album?id=${id}`);
        break;
      case 'song':
        router.push(`/song?id=${id}`);
        break;
      case 'recording':
        router.push(`/recording?id=${id}`);
        break;
      default:
        console.warn(`Unknown result type: ${type}`);
    }
  };

  // Close results when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSignOut = async () => {
    await signOut();
  };

    const pathname = usePathname();
  

   if(isAuthOnlyRoute(pathname) || isOnboarding(pathname) || isStandaloneRoute(pathname)){
      return null;
    }

  return (
    <header className="sticky top-0 z-50 flex justify-between items-center p-6 bg-background border-b backdrop-blur-sm bg-background/95">
      {/* Left: Logo */}
      <div className="flex items-center mr-2" onClick={() => router.push("/")}>
        <Image src="/SMASH-(full)-logo.png" width={120} height={120} alt="logo" />
      </div>
      {/* Center: Search Bar */}
      <div className="flex-1 flex justify-center">
        <div className="relative w-full max-w-xl" ref={searchRef}>
          <div className="flex items-center bg-muted rounded-xl border border-muted-foreground/10">
            {/* Entity Type Dropdown */}
            <div className="flex-shrink-0">
              <Select value={entityType} onValueChange={setEntityType}>
                <SelectTrigger className="border-0 bg-transparent rounded-l-xl rounded-r-none h-10 w-24 text-xs">
                  <SelectValue placeholder="All" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="Artist">Artist</SelectItem>
                  <SelectItem value="Album">Album</SelectItem>
                  <SelectItem value="Song">Song</SelectItem>
                  <SelectItem value="Recording">Recording</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Search Input */}
            <div className="flex-1 flex items-center px-4 py-2">
              <Search className="w-5 h-5 text-muted-foreground mr-2" />
              <input
                type="text"
                placeholder="Search music, artists, albums..."
                value={searchTerm}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="flex-1 bg-transparent outline-none text-base placeholder:text-muted-foreground"
              />
              {searchLoading && (
                <Loader2 className="w-4 h-4 text-muted-foreground animate-spin ml-2" />
              )}
            </div>
          </div>

          {/* Search Results Dropdown */}
          {showResults && (searchResults.length > 0 || searchLoading) && (
            <div className="absolute top-full left-0 right-0 mt-1 bg-popover border rounded-md shadow-md z-50 max-h-96 overflow-y-auto">
              {searchLoading ? (
                <div className="p-4 text-center text-muted-foreground">
                  <Loader2 className="w-4 h-4 animate-spin mx-auto mb-2" />
                  Searching...
                </div>
              ) : searchResults.length > 0 ? (
                <div className="py-2">
                  {searchResults.map((result) => (
                    <div
                      key={`${result.type}-${result.id}`}
                      onClick={() => handleResultClick(result)}
                      className="px-4 py-3 hover:bg-muted cursor-pointer border-b border-muted/50 last:border-b-0"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="font-medium text-sm">{result.displayName}</div>
                          <div className="text-xs text-muted-foreground capitalize">
                            {result.type}
                          </div>
                        </div>
                        {/* <div className="text-xs text-muted-foreground">
                          Score: {result.searchScore?.toFixed(2)}
                        </div> */}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="p-4 text-center text-muted-foreground text-sm">
                  No results found
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      {/* Right: Premium, Bell, Settings, Avatar Dropdown */}
      <div className="flex items-center gap-4 ml-4">
        {/* <Button variant="outline" className="rounded-full px-6 py-2 font-medium">
          Explore Premium
        </Button> */}
        {/* <Bell className="w-6 h-6 cursor-pointer text-muted-foreground" /> */}
        {/* Settings Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="w-9 h-9 rounded-full flex items-center justify-center focus:outline-none">
              <Settings className="w-6 h-6 text-muted-foreground" />
            </button>
          </DropdownMenuTrigger>
         <DropdownMenuContent align="end" className="min-w-[200px] px-2 py-2 space-y-2">
  <div className="text-xs text-muted-foreground font-medium px-3 pt-1">
  Preferences
</div>
<LanguageToggle />
<ThemeToggle />

<div className="border-t border-muted my-1" />
<div
  onClick={handleSignOut}
  className="flex w-full gap-2 justify-start items-center px-3 py-2 rounded-sm hover:bg-muted cursor-pointer text-sm"
>
  <LogOut className="w-4 h-4" />
  <span>Sign Out</span>
</div>

</DropdownMenuContent>

        </DropdownMenu>
        <DropdownMenu>
  <DropdownMenuTrigger asChild>
    <button className="w-9 h-9 rounded-full bg-muted flex items-center justify-center focus:outline-none group">
      {isAuthenticated && user && (user.email || user.username) ? (
        <span className="w-7 h-7 rounded-full bg-primary/90 text-white flex items-center justify-center font-semibold uppercase select-none">
          {((user.email || user.username)?.[0] || '').toUpperCase()}
        </span>
      ) : (
        <span className="w-7 h-7 rounded-full bg-muted-foreground/30 flex items-center justify-center text-muted-foreground font-semibold select-none">
          <User className="w-4 h-4" />
        </span>
      )}
    </button>
  </DropdownMenuTrigger>

  <DropdownMenuContent align="end" className="min-w-[180px]">
    {!isLoading && isAuthenticated && (
      <div className="px-3 py-2 text-sm text-muted-foreground select-none pointer-events-none">
        Welcome, {user?.email || user?.username}
      </div>
    )}

    {!isLoading && !isAuthenticated && (
      <>
        <DropdownMenuItem
          onClick={() => router.push('/login')}
          className="gap-2 cursor-pointer"
        >
          <LogIn className="w-4 h-4" /> Sign In
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => router.push('/signup')}
          className="gap-2 cursor-pointer"
        >
          <User className="w-4 h-4" /> Sign Up
        </DropdownMenuItem>
      </>
    )}
  </DropdownMenuContent>
</DropdownMenu>

      </div>
    </header>
  );
}
