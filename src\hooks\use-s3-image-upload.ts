/**
 * Custom React Hook for S3 Image Upload
 * Provides a clean API for components to handle image uploads with state management
 */

import { useState, useCallback, useRef } from 'react';
import { uploadImageToS3, validateImageFile } from '@/lib/s3-image-upload';
import type {
  ImageUploadState,
} from '@/types/s3-upload';

export interface UseS3ImageUploadReturn {
  // Simple state
  imageUrl: string | null; // The URL to display (S3 URL or local preview)
  isUploading: boolean;
  progress: number; // 0-100
  error: string | null;

  // Simple actions
  uploadImage: (file: File, onSuccess?: (url: string) => void) => Promise<boolean>; // Returns true if successful
  clearImage: () => void;
}

/**
 * Simplified hook for S3 image uploads
 * Handles all complexity internally and provides a clean, simple API
 */
export function useS3ImageUpload(): UseS3ImageUploadReturn {
  const [uploadState, setUploadState] = useState<ImageUploadState>({
    isUploading: false,
    progress: 0,
    error: null,
    uploadedUrl: null,
    previewUrl: null,
  });

  // Keep track of current upload to prevent concurrent uploads
  const currentUploadRef = useRef<AbortController | null>(null);

  /**
   * Simplified upload function - handles validation, preview, and upload automatically
   */
  const uploadImage = useCallback(async (file: File, onSuccess?: (url: string) => void): Promise<boolean> => {
    // Prevent concurrent uploads
    if (uploadState.isUploading) {
      return false;
    }

    // Cancel any existing upload
    if (currentUploadRef.current) {
      currentUploadRef.current.abort();
    }

    // Create new abort controller for this upload
    currentUploadRef.current = new AbortController();

    try {
      // Step 1: Validate the image
      const validation = await validateImageFile(file);
      if (!validation.isValid) {
        setUploadState(prev => ({
          ...prev,
          error: validation.error || 'Invalid image file',
        }));
        return false;
      }

      // Step 2: Create immediate preview and start upload
      const previewUrl = URL.createObjectURL(file);
      setUploadState({
        isUploading: true,
        progress: 0,
        error: null,
        uploadedUrl: null,
        previewUrl,
      });

      // Step 3: Upload to S3 with progress tracking
      const result = await uploadImageToS3(file, {
        onProgress: (progress) => {
          setUploadState(prev => ({
            ...prev,
            progress,
          }));
        },
      });

      if (result.success && result.url) {
        // Upload successful - replace preview with S3 URL
        URL.revokeObjectURL(previewUrl);
        setUploadState({
          isUploading: false,
          progress: 100,
          error: null,
          uploadedUrl: result.url,
          previewUrl: null,
        });

        // Call success callback if provided
        if (onSuccess) {
          onSuccess(result.url);
        }

        return true;
      } else {
        // Upload failed - keep preview but show error
        setUploadState(prev => ({
          ...prev,
          isUploading: false,
          progress: 0,
          error: result.error || 'Upload failed',
        }));
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      setUploadState(prev => ({
        ...prev,
        isUploading: false,
        progress: 0,
        error: errorMessage,
      }));
      return false;
    } finally {
      currentUploadRef.current = null;
    }
  }, [uploadState.isUploading]);

  /**
   * Clear image and reset state
   */
  const clearImage = useCallback(() => {
    // Cancel any ongoing upload
    if (currentUploadRef.current) {
      currentUploadRef.current.abort();
      currentUploadRef.current = null;
    }

    // Cleanup preview URL
    if (uploadState.previewUrl) {
      URL.revokeObjectURL(uploadState.previewUrl);
    }

    setUploadState({
      isUploading: false,
      progress: 0,
      error: null,
      uploadedUrl: null,
      previewUrl: null,
    });
  }, [uploadState.previewUrl]);

  // Get the URL to display (S3 URL takes priority over preview)
  const imageUrl = uploadState.uploadedUrl || uploadState.previewUrl;

  return {
    imageUrl,
    isUploading: uploadState.isUploading,
    progress: uploadState.progress,
    error: uploadState.error,
    uploadImage,
    clearImage,
  };
}


