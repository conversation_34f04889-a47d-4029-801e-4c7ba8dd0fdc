"use client"

/**
 * Safari iOS viewport height utility
 * 
 * Handles dynamic viewport height changes in Safari iOS where the address bar
 * and toolbar can show/hide, affecting the viewport height.
 */

let isInitialized = false;

/**
 * Initialize viewport height handling for Safari iOS
 * This should be called once when the app loads
 */
export function initializeViewportHeight() {
  if (isInitialized || typeof window === 'undefined') return;
  
  isInitialized = true;
  
  // Function to update the viewport height CSS custom property
  const updateViewportHeight = () => {
    // Get the actual viewport height
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
    
    // Also update our custom viewport height variable
    document.documentElement.style.setProperty('--viewport-height', `${window.innerHeight}px`);
  };
  
  // Set initial values
  updateViewportHeight();
  
  // Update on resize (handles Safari's dynamic viewport changes)
  let resizeTimer: NodeJS.Timeout;
  window.addEventListener('resize', () => {
    // Debounce resize events to avoid excessive updates
    clearTimeout(resizeTimer);
    resizeTimer = setTimeout(updateViewportHeight, 100);
  });
  
  // Update on orientation change (important for mobile)
  window.addEventListener('orientationchange', () => {
    // Delay to allow the browser to update the viewport
    setTimeout(updateViewportHeight, 500);
  });
  
  // Update on visual viewport changes (Safari specific)
  if (window.visualViewport) {
    window.visualViewport.addEventListener('resize', updateViewportHeight);
  }
}

/**
 * Get the current safe viewport height
 * This accounts for Safari's dynamic UI elements
 */
export function getSafeViewportHeight(): number {
  if (typeof window === 'undefined') return 0;
  
  // Use visual viewport if available (more accurate for Safari)
  if (window.visualViewport) {
    return window.visualViewport.height;
  }
  
  return window.innerHeight;
}

/**
 * Check if the current browser is Safari on iOS
 */
export function isSafariIOS(): boolean {
  if (typeof window === 'undefined') return false;
  
  const userAgent = window.navigator.userAgent;
  const isIOS = /iPad|iPhone|iPod/.test(userAgent);
  const isSafari = /Safari/.test(userAgent) && !/Chrome|CriOS|FxiOS/.test(userAgent);
  
  return isIOS && isSafari;
}
