# S3 Image Upload System - Usage Guide

A comprehensive, reusable image upload system for uploading images to AWS S3 using pre-signed URLs.

## 📋 Table of Contents

- [Overview](#overview)
- [Quick Start](#quick-start)
- [API Reference](#api-reference)
- [Components](#components)
- [Examples](#examples)
- [Configuration](#configuration)
- [Error Handling](#error-handling)
- [Best Practices](#best-practices)

## 🎯 Overview

The S3 Image Upload System provides:
- **Secure uploads** using AWS S3 pre-signed URLs
- **Real-time progress tracking** with progress bars
- **Image validation** (type, size, dimensions)
- **Immediate preview** with local blob URLs
- **Error handling** with user-friendly messages
- **TypeScript support** with full type safety

## 🚀 Quick Start

### 1. Import the Hook

```typescript
import { useS3ImageUpload } from '@/hooks/use-s3-image-upload';
```

### 2. Super Simple Usage

```typescript
export default function MyComponent() {
  const { imageUrl, isUploading, progress, error, uploadImage } = useS3ImageUpload();

  const handleFileSelect = async (file: File) => {
    const success = await uploadImage(file);
    if (success) {
      console.log('Upload successful!');
    }
  };

  return (
    <div>
      <input
        type="file"
        accept="image/*"
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file) handleFileSelect(file);
        }}
      />
      {isUploading && <p>Uploading... {progress}%</p>}
      {error && <p>Error: {error}</p>}
      {imageUrl && <img src={imageUrl} alt="Uploaded" />}
    </div>
  );
}
```

### 3. Complete Example with Clear Button

```typescript
export default function CompleteUploader() {
  const { imageUrl, isUploading, progress, error, uploadImage, clearImage } = useS3ImageUpload();

  const handleFileSelect = async (file: File) => {
    const success = await uploadImage(file);
    if (success) {
      console.log('Upload successful!');
    }
  };

  return (
    <div className="space-y-4">
      <input
        type="file"
        accept="image/*"
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file) handleFileSelect(file);
        }}
        disabled={isUploading}
      />

      {isUploading && (
        <div>
          <p>Uploading... {progress}%</p>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>
      )}

      {error && <p className="text-red-500">Error: {error}</p>}

      {imageUrl && (
        <div>
          <img src={imageUrl} alt="Uploaded" className="max-w-xs rounded" />
          <button onClick={clearImage} className="mt-2 px-4 py-2 bg-red-500 text-white rounded">
            Remove Image
          </button>
        </div>
      )}
    </div>
  );
}
```

## 📚 API Reference

### Hook: `useS3ImageUpload()`

**Super simple hook** - handles all complexity internally!

#### Returns
```typescript
{
  imageUrl: string | null;        // The URL to display (auto-managed)
  isUploading: boolean;           // Upload in progress
  progress: number;               // Upload progress (0-100)
  error: string | null;           // Error message if any
  uploadImage: (file: File) => Promise<boolean>;  // Upload function
  clearImage: () => void;         // Clear image and reset
}
```

#### Usage
```typescript
const { imageUrl, isUploading, progress, error, uploadImage, clearImage } = useS3ImageUpload();

// Upload a file
const success = await uploadImage(file);

// Display the image
{imageUrl && <img src={imageUrl} alt="Image" />}

// Clear everything
clearImage();
```

**That's it!** The hook handles:
- ✅ **Validation** - Automatically validates file type, size, dimensions
- ✅ **Preview** - Shows immediate preview while uploading
- ✅ **Upload** - Uploads to S3 with progress tracking
- ✅ **URL Management** - Automatically switches from preview to S3 URL
- ✅ **Error Handling** - Clear error messages
- ✅ **Cleanup** - Automatic memory management



### Utility Functions

```typescript
import {
  validateImageFile,
  uploadImageToS3,
  generateS3Filename,
} from '@/lib/s3-image-upload';

// Validate image without uploading
const validation = await validateImageFile(file);

// Direct upload (without hook)
const result = await uploadImageToS3(file, {
  onProgress: (progress) => console.log(`${progress}%`)
});
```

## 🧩 Components

### Basic Upload Component

```typescript
import React from 'react';
import { useS3ImageUpload } from '@/hooks/use-s3-image-upload';

export function ImageUploader({ onUploadComplete }: {
  onUploadComplete: (url: string) => void
}) {
  const { imageUrl, isUploading, progress, error, uploadImage } = useS3ImageUpload();

  const handleFileSelect = async (file: File) => {
    const success = await uploadImage(file);
    if (success && imageUrl) {
      onUploadComplete(imageUrl);
    }
  };

  return (
    <div className="space-y-4">
      <input
        type="file"
        accept="image/*"
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file) handleFileSelect(file);
        }}
        disabled={isUploading}
      />

      {isUploading && (
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div className="bg-blue-600 h-2 rounded-full" style={{ width: `${progress}%` }} />
        </div>
      )}

      {error && <p className="text-red-500 text-sm">{error}</p>}

      {imageUrl && <img src={imageUrl} alt="Preview" className="max-w-xs rounded" />}
    </div>
  );
}
```

### Drag & Drop Upload Component

```typescript
import React, { useState } from 'react';
import { useS3ImageUpload } from '@/hooks/use-s3-image-upload';

export function DragDropUploader() {
  const [isDragOver, setIsDragOver] = useState(false);
  const { imageUrl, isUploading, progress, error, uploadImage } = useS3ImageUpload();

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const file = e.dataTransfer.files[0];
    if (file && file.type.startsWith('image/')) {
      await uploadImage(file);
    }
  };

  return (
    <div className="space-y-4">
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center ${
          isDragOver ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
        }`}
        onDrop={handleDrop}
        onDragOver={(e) => {
          e.preventDefault();
          setIsDragOver(true);
        }}
        onDragLeave={() => setIsDragOver(false)}
      >
        {isUploading ? (
          <div>
            <p>Uploading... {progress}%</p>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div className="bg-blue-600 h-2 rounded-full" style={{ width: `${progress}%` }} />
            </div>
          </div>
        ) : (
          <p>Drag and drop an image here</p>
        )}
      </div>

      {error && <p className="text-red-500">{error}</p>}
      {imageUrl && <img src={imageUrl} alt="Uploaded" className="max-w-xs rounded mx-auto" />}
    </div>
  );
}
```

## ⚙️ Configuration

### Image Validation Settings

```typescript
import { DEFAULT_IMAGE_UPLOAD_CONFIG } from '@/types/s3-upload';

// Default configuration:
{
  maxSizeBytes: 5 * 1024 * 1024, // 5MB
  maxWidth: 2048,
  maxHeight: 2048,
  minWidth: 100,
  minHeight: 100,
  allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  allowedExtensions: ['.jpg', '.jpeg', '.png', '.webp'],
}
```

### API Configuration

```typescript
import { S3_UPLOAD_API_CONFIG } from '@/types/s3-upload';

// API settings:
{
  endpoint: 'https://57k50sc89a.execute-api.us-east-2.amazonaws.com/staging/generate-signed-url',
  filePathPrefix: 'users/example_name/',
}
```

## 🚨 Error Handling

### Common Error Scenarios

The hook automatically handles validation and shows clear error messages:

```typescript
const { error, uploadImage } = useS3ImageUpload();

const handleUpload = async (file: File) => {
  const success = await uploadImage(file);

  if (!success) {
    // Error is automatically displayed via the `error` state
    console.log('Current error:', error);

    // Common error messages:
    // "Please select a supported image file. We support: JPG, JPEG, PNG, and WebP formats only."
    // "Image size must be less than 5MB. Current size: 8.2MB"
    // "Image dimensions must be at least 100x100px. Current: 50x50px"
    // "API request failed: 500 Internal Server Error"
    // "S3 upload failed: Network error"
  }
};
```

### Supported File Types

**✅ Supported:**
- JPG/JPEG
- PNG
- WebP

**❌ Not Supported:**
- SVG (security reasons)
- GIF (not needed for profile photos)
- Other formats

The hook will show a clear error message: *"Please select a supported image file. We support: JPG, JPEG, PNG, and WebP formats only."*

### Error Recovery

```typescript
const { uploadState, uploadImage, clearUpload } = useS3ImageUpload();

// Retry upload
const handleRetry = async () => {
  if (uploadState.error && lastFile) {
    clearUpload(); // Clear error state
    await uploadImage(lastFile);
  }
};
```

## ✅ Best Practices

### 1. Always Validate Before Upload

```typescript
const handleFileSelect = async (file: File) => {
  // Validate first
  const validation = await validateImage(file);
  if (!validation.isValid) {
    alert(validation.error);
    return;
  }
  
  // Then upload
  await uploadImage(file);
};
```

### 2. Provide User Feedback

```typescript
// Show progress
{isUploading && <Progress value={uploadState.progress} />}

// Show errors
{hasError && <Alert>{uploadState.error}</Alert>}

// Show success
{hasUploadedImage && <CheckIcon />}
```

### 3. Handle Memory Management

```typescript
useEffect(() => {
  // Cleanup on unmount
  return () => {
    if (uploadState.previewUrl) {
      URL.revokeObjectURL(uploadState.previewUrl);
    }
  };
}, [uploadState.previewUrl]);
```

### 4. Disable UI During Upload

```typescript
<Button disabled={isUploading}>
  {isUploading ? 'Uploading...' : 'Upload Image'}
</Button>
```

### 5. Use TypeScript Types

```typescript
import type {
  ImageUploadResult,
  ImageUploadState,
  ImageValidationResult,
} from '@/types/s3-upload';
```

## 🔗 Integration Examples

### Form Integration

```typescript
const [imageUrl, setImageUrl] = useState<string>('');
const { uploadImage } = useS3ImageUpload();

const handleSubmit = async (formData: FormData) => {
  // Include uploaded image URL in form submission
  const data = {
    ...formData,
    profileImage: imageUrl,
  };
  
  await submitForm(data);
};
```

### Multiple Images

```typescript
const [uploadedImages, setUploadedImages] = useState<string[]>([]);

const handleMultipleUploads = async (files: FileList) => {
  const uploadPromises = Array.from(files).map(file => uploadImage(file));
  const results = await Promise.all(uploadPromises);
  
  const successfulUploads = results
    .filter(result => result.success)
    .map(result => result.url!);
    
  setUploadedImages(prev => [...prev, ...successfulUploads]);
};
```

---

## 📞 Support

For issues or questions about the S3 Image Upload System:
1. Check the error messages for specific guidance
2. Verify your API endpoint is accessible
3. Ensure your AWS S3 bucket has proper CORS configuration
4. Review the TypeScript types for proper usage

The system is designed to be robust and user-friendly, with comprehensive error handling and clear feedback for all scenarios.

---

## 🎯 Summary

**Super Simple API**: `useS3ImageUpload()` gives you everything you need in 6 simple properties:

```typescript
const { imageUrl, isUploading, progress, error, uploadImage, clearImage } = useS3ImageUpload();
```

**That's it!** No complex state management, no manual validation, no URL juggling.

**What the hook handles automatically**:
- ✅ **File validation** - Type, size, dimensions
- ✅ **Immediate preview** - Shows image instantly while uploading
- ✅ **S3 upload** - Handles API calls and file upload
- ✅ **Progress tracking** - Real-time upload progress
- ✅ **URL management** - Switches from preview to S3 URL automatically
- ✅ **Error handling** - Clear, user-friendly error messages
- ✅ **Memory cleanup** - No memory leaks from blob URLs

**Developer Experience**:
- **3 lines of code** to add image upload to any component
- **No configuration** needed - works out of the box
- **Production ready** - Used in both onboarding flows
- **TypeScript safe** - Full type safety with zero `any` types

**Perfect for**: Profile photos, product images, avatars, gallery uploads, or any image upload need!
